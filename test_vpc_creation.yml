---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        test_vpc_creation.yml                                             #
# Version:                                                                        #
#               2024-10-04 Test VPC creation with Terraform and Ansible          #
# Create Date:  2024-10-04                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Test playbook for VPC creation using aws_tfvpc role               #
#                                                                                 #
# Usage:                                                                          #
#   ansible-playbook test_vpc_creation.yml                                       #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Test VPC Creation with Terraform and Ansible
  hosts: localhost
  gather_facts: false
  vars:
    # Common AWS configuration
    aws_common_state: present
    aws_common_config:
      max_attempts: 50
      
    # VPC-specific configuration for testing
    aws_tfvpc_config:
      vpc_cidr: "**********/16"
      enable_dns_hostnames: true
      enable_dns_support: true
      public_subnet_cidrs:
        - "**********/24"
        - "**********/24"
      private_subnet_cidrs:
        - "***********/24"
        - "***********/24"
      availability_zones: []  # Auto-detect AZs
      vpc_init: true

  tasks:
    # ------------------------------------------------------------------------------- #
    - name: Display test configuration
      ansible.builtin.debug:
        msg: |
          Starting VPC Creation Test
          
          Configuration:
          - VPC CIDR: {{ aws_tfvpc_config.vpc_cidr }}
          - Public Subnets: {{ aws_tfvpc_config.public_subnet_cidrs | join(', ') }}
          - Private Subnets: {{ aws_tfvpc_config.private_subnet_cidrs | join(', ') }}
          - State: {{ aws_common_state }}

    # ------------------------------------------------------------------------------- #
    - name: Test VPC creation using aws_tfvpc role
      ansible.builtin.include_role:
        name: aws_tfvpc
        tasks_from: aws_tfvpc_main
      vars:
        aws_common_state: "{{ aws_common_state }}"
        aws_tfvpc_config: "{{ aws_tfvpc_config }}"

    # ------------------------------------------------------------------------------- #
    - name: Display test results
      ansible.builtin.debug:
        msg: |
          VPC Creation Test Results:
          
          Test completed successfully!
          
          Next steps:
          1. Check AWS Console for created VPC resources
          2. Verify VPC CIDR: {{ aws_tfvpc_config.vpc_cidr }}
          3. Verify subnets are created in correct AZs
          4. Check that NAT Gateways are provisioned
          5. Validate route tables are configured correctly
          
          To clean up resources, run:
          ansible-playbook test_vpc_creation.yml -e "aws_common_state=absent"

    # ------------------------------------------------------------------------------- #
    - name: Display variable outputs for debugging
      vars:
        debug_outputs:
          aws_caller_info: "{{ aws_caller_info | default('Not available') }}"
          aws_common_defaults: "{{ aws_common_defaults | default('Not available') }}"
          aws_common_config: "{{ aws_common_config | default('Not available') }}"
          aws_common_vars: "{{ aws_common_vars | default('Not available') }}"
          aws_common_facts: "{{ aws_common_facts | default('Not available') }}"
          aws_tfvpc_config: "{{ aws_tfvpc_config | default('Not available') }}"
          terraform_output: "{{ aws_tfvpc_terraform_output | default('Not available') }}"
      ansible.builtin.debug:
        var: debug_outputs
        verbosity: 1
