---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        vpc_creation_playbook.yml                                         #
# Version:                                                                        #
#               2024-10-04 Initial implementation                                 #
# Create Date:  2024-10-04                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Example playbook demonstrating VPC creation using Terraform      #
#               and Ansible integration with the aws_tfvpc role                  #
#                                                                                 #
# Usage:                                                                          #
#   ansible-playbook -i inventory examples/vpc_creation_playbook.yml             #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Create AWS VPC using Terraform and Ansible
  hosts: localhost
  gather_facts: false
  vars:
    # Common AWS configuration
    aws_common_state: present  # Use 'absent' to destroy resources
    aws_common_config:
      max_attempts: 50
      
    # VPC-specific configuration
    aws_tfvpc_config:
      vpc_cidr: "10.0.0.0/16"
      enable_dns_hostnames: true
      enable_dns_support: true
      public_subnet_cidrs:
        - "********/24"
        - "********/24"
      private_subnet_cidrs:
        - "*********/24"
        - "*********/24"
      availability_zones: []  # Auto-detect AZs
      vpc_init: true

  tasks:
    # ------------------------------------------------------------------------------- #
    - name: Create VPC infrastructure
      ansible.builtin.include_role:
        name: lmco.aws.aws_tfvpc
        tasks_from: aws_tfvpc_main
      vars:
        aws_common_state: "{{ aws_common_state }}"
        aws_tfvpc_config: "{{ aws_tfvpc_config }}"

    # ------------------------------------------------------------------------------- #
    - name: Display VPC creation results
      ansible.builtin.debug:
        msg: |
          VPC Creation Complete!
          
          Configuration used:
          - VPC CIDR: {{ aws_tfvpc_config.vpc_cidr }}
          - Public Subnets: {{ aws_tfvpc_config.public_subnet_cidrs | join(', ') }}
          - Private Subnets: {{ aws_tfvpc_config.private_subnet_cidrs | join(', ') }}
          - DNS Support: {{ aws_tfvpc_config.enable_dns_support }}
          - DNS Hostnames: {{ aws_tfvpc_config.enable_dns_hostnames }}
          
          Next steps:
          1. Check AWS Console to verify VPC creation
          2. Note the VPC ID and subnet IDs for future use
          3. Configure security groups as needed
          4. Deploy applications to the new VPC

# ------------------------------------------------------------------------------- #
# Alternative example with custom configuration
# ------------------------------------------------------------------------------- #
- name: Create Custom VPC Configuration
  hosts: localhost
  gather_facts: false
  vars:
    aws_common_state: present
    aws_common_config:
      max_attempts: 50
      
    # Custom VPC configuration for production environment
    aws_tfvpc_config:
      vpc_cidr: "**********/16"
      enable_dns_hostnames: true
      enable_dns_support: true
      public_subnet_cidrs:
        - "**********/24"
        - "**********/24"
        - "**********/24"
      private_subnet_cidrs:
        - "***********/24"
        - "***********/24"
        - "***********/24"
      availability_zones:
        - "us-east-1a"
        - "us-east-1b"
        - "us-east-1c"
      vpc_init: true

  tasks:
    - name: Create production VPC with custom configuration
      ansible.builtin.include_role:
        name: lmco.aws.aws_tfvpc
        tasks_from: aws_tfvpc_main
      vars:
        aws_common_state: "{{ aws_common_state }}"
        aws_tfvpc_config: "{{ aws_tfvpc_config }}"

    - name: Display production VPC details
      ansible.builtin.debug:
        msg: |
          Production VPC Created!
          - VPC CIDR: {{ aws_tfvpc_config.vpc_cidr }}
          - Availability Zones: {{ aws_tfvpc_config.availability_zones | join(', ') }}
          - Public Subnets: {{ aws_tfvpc_config.public_subnet_cidrs | length }}
          - Private Subnets: {{ aws_tfvpc_config.private_subnet_cidrs | length }}
