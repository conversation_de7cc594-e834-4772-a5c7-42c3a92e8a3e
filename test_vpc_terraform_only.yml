---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        test_vpc_terraform_only.yml                                       #
# Version:                                                                        #
#               2024-10-04 Test VPC Terraform module directly                     #
# Create Date:  2024-10-04                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Direct test of VPC Terraform module without full role dependencies #
#                                                                                 #
# Usage:                                                                          #
#   ansible-playbook test_vpc_terraform_only.yml                                 #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Test VPC Terraform Module Directly
  hosts: localhost
  gather_facts: true
  vars:
    # Test configuration
    test_vpc_config:
      account: "************"
      region: "us-east-1"
      environment: "test"
      vpc_cidr: "**********/16"
      enable_dns_hostnames: true
      enable_dns_support: true
      public_subnet_cidrs:
        - "**********/24"
        - "**********/24"
      private_subnet_cidrs:
        - "***********/24"
        - "***********/24"
      availability_zones: []
      vpc_init: true
      prefix: "test-vpc"
      tags:
        Environment: "test"
        Project: "vpc-test"
        Owner: "augment-agent"

  tasks:
    # ------------------------------------------------------------------------------- #
    - name: Display test configuration
      ansible.builtin.debug:
        msg: |
          Testing VPC Terraform Module
          
          Configuration:
          - VPC CIDR: {{ test_vpc_config.vpc_cidr }}
          - Public Subnets: {{ test_vpc_config.public_subnet_cidrs | join(', ') }}
          - Private Subnets: {{ test_vpc_config.private_subnet_cidrs | join(', ') }}
          - Region: {{ test_vpc_config.region }}

    # ------------------------------------------------------------------------------- #
    - name: Create temporary directory for Terraform files
      ansible.builtin.tempfile:
        state: directory
        prefix: "vpc-test."
        suffix: ".tf"
      register: temp_dir

    # ------------------------------------------------------------------------------- #
    - name: Create test Terraform configuration
      ansible.builtin.copy:
        content: |
          terraform {
            required_version = ">= 1.0"
            required_providers {
              aws = {
                source  = "hashicorp/aws"
                version = "~> 5.33"
              }
            }
          }

          provider "aws" {
            region = "{{ test_vpc_config.region }}"
            # Note: This will use default AWS credentials from environment or ~/.aws/credentials
          }

          module "vpc" {
            source = "{{ ansible_env.PWD }}/terraform/modules/vpc"
            
            account                = "{{ test_vpc_config.account }}"
            region                 = "{{ test_vpc_config.region }}"
            environment            = "{{ test_vpc_config.environment }}"
            vpc_cidr              = "{{ test_vpc_config.vpc_cidr }}"
            enable_dns_hostnames  = {{ test_vpc_config.enable_dns_hostnames | lower }}
            enable_dns_support    = {{ test_vpc_config.enable_dns_support | lower }}
            public_subnet_cidrs   = {{ test_vpc_config.public_subnet_cidrs | to_json }}
            private_subnet_cidrs  = {{ test_vpc_config.private_subnet_cidrs | to_json }}
            availability_zones    = {{ test_vpc_config.availability_zones | to_json }}
            vpc_init              = {{ test_vpc_config.vpc_init | lower }}
            
            common_data = {
              prefix = "{{ test_vpc_config.prefix }}"
              tags   = {{ test_vpc_config.tags | to_json }}
            }
          }

          output "vpc_outputs" {
            description = "VPC module outputs"
            value = module.vpc.vpc_data
          }
        dest: "{{ temp_dir.path }}/main.tf"

    # ------------------------------------------------------------------------------- #
    - name: Initialize Terraform
      ansible.builtin.command:
        cmd: terraform init
        chdir: "{{ temp_dir.path }}"
      register: terraform_init

    # ------------------------------------------------------------------------------- #
    - name: Display Terraform init results
      ansible.builtin.debug:
        var: terraform_init

    # ------------------------------------------------------------------------------- #
    - name: Validate Terraform configuration
      ansible.builtin.command:
        cmd: terraform validate
        chdir: "{{ temp_dir.path }}"
      register: terraform_validate

    # ------------------------------------------------------------------------------- #
    - name: Display Terraform validation results
      ansible.builtin.debug:
        var: terraform_validate

    # ------------------------------------------------------------------------------- #
    - name: Plan Terraform deployment (dry run)
      ansible.builtin.command:
        cmd: terraform plan
        chdir: "{{ temp_dir.path }}"
      register: terraform_plan
      failed_when: false  # Don't fail if plan has issues

    # ------------------------------------------------------------------------------- #
    - name: Display Terraform plan results
      ansible.builtin.debug:
        var: terraform_plan

    # ------------------------------------------------------------------------------- #
    - name: Clean up temporary directory
      ansible.builtin.file:
        path: "{{ temp_dir.path }}"
        state: absent

    # ------------------------------------------------------------------------------- #
    - name: Display test summary
      ansible.builtin.debug:
        msg: |
          VPC Terraform Module Test Summary:
          
          ✓ Terraform configuration created
          ✓ Terraform initialized: {{ 'SUCCESS' if terraform_init.rc == 0 else 'FAILED' }}
          ✓ Terraform validated: {{ 'SUCCESS' if terraform_validate.rc == 0 else 'FAILED' }}
          ✓ Terraform plan: {{ 'SUCCESS' if terraform_plan.rc == 0 else 'FAILED' }}
          
          {% if terraform_plan.rc == 0 %}
          The VPC Terraform module is ready for deployment!
          
          To actually create the VPC, you would run:
          terraform apply
          
          This would create:
          - VPC with CIDR {{ test_vpc_config.vpc_cidr }}
          - {{ test_vpc_config.public_subnet_cidrs | length }} public subnets
          - {{ test_vpc_config.private_subnet_cidrs | length }} private subnets
          - Internet Gateway
          - NAT Gateways
          - Route Tables
          - Security Groups
          {% else %}
          There were issues with the Terraform configuration.
          Check the plan output above for details.
          {% endif %}
