#############################################################################################
#                                                                                           #
# Title:        vpc_main.tf                                                                 #
# Version:                                                                                  #
#               2024-10-04 Enhanced for comprehensive VPC creation                          #
#               2021-11-09 WRC. Add provider def to eliminate warnings in latest version    #
#               2020-12-07 WRC. Add outputs                                                 #
#               2020-11-10 WRC. Converge variables and outputs in this file.                #
#               2020-06-09 WRC. Initial                                                     #
# Create Date:  2020-06-09                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#               Comprehensive VPC module with subnets, gateways, and routing               #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Variables Section                                                                         #
#-------------------------------------------------------------------------------------------#
variable "account"      { type = string }
variable "region"       { type = string }
variable "environment"  { type = string }
variable "vpc_init"     {
                          type = bool
                          default = false
                        }
variable "common_data" {}

# VPC Configuration Variables
variable "vpc_cidr" {
  type        = string
  description = "CIDR block for the VPC"
  default     = "10.0.0.0/16"
}

variable "enable_dns_hostnames" {
  type        = bool
  description = "Enable DNS hostnames in the VPC"
  default     = true
}

variable "enable_dns_support" {
  type        = bool
  description = "Enable DNS support in the VPC"
  default     = true
}

variable "public_subnet_cidrs" {
  type        = list(string)
  description = "CIDR blocks for public subnets"
  default     = ["********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  type        = list(string)
  description = "CIDR blocks for private subnets"
  default     = ["*********/24", "*********/24"]
}

variable "availability_zones" {
  type        = list(string)
  description = "Availability zones for subnets"
  default     = []
}

#-------------------------------------------------------------------------------------------#
# Data Section                                                                              #
#-------------------------------------------------------------------------------------------#
data "aws_availability_zones" "available" {
  state = "available"
}

#-------------------------------------------------------------------------------------------#
# Locals Section                                                                            #
#-------------------------------------------------------------------------------------------#
locals {
  # Use provided AZs or default to first available ones
  azs = length(var.availability_zones) > 0 ? var.availability_zones : slice(data.aws_availability_zones.available.names, 0, length(var.public_subnet_cidrs))

  # Common tags from common_data
  common_tags = try(var.common_data.tags, {})

  # VPC name prefix
  name_prefix = try(var.common_data.prefix, "${var.environment}-${var.region}")
}

#-------------------------------------------------------------------------------------------#
# VPC Resources                                                                             #
#-------------------------------------------------------------------------------------------#
resource "aws_vpc" "main" {
  count = var.vpc_init ? 1 : 0

  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-vpc"
      Type = "vpc"
    }
  )
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  count = var.vpc_init ? 1 : 0

  vpc_id = aws_vpc.main[0].id

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-igw"
      Type = "internet-gateway"
    }
  )
}

# Public Subnets
resource "aws_subnet" "public" {
  count = var.vpc_init ? length(var.public_subnet_cidrs) : 0

  vpc_id                  = aws_vpc.main[0].id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = local.azs[count.index]
  map_public_ip_on_launch = true

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-public-subnet-${count.index + 1}"
      Type = "public-subnet"
      AZ   = local.azs[count.index]
    }
  )
}

# Private Subnets
resource "aws_subnet" "private" {
  count = var.vpc_init ? length(var.private_subnet_cidrs) : 0

  vpc_id            = aws_vpc.main[0].id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = local.azs[count.index]

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-private-subnet-${count.index + 1}"
      Type = "private-subnet"
      AZ   = local.azs[count.index]
    }
  )
}

# Elastic IPs for NAT Gateways
resource "aws_eip" "nat" {
  count = var.vpc_init ? length(var.private_subnet_cidrs) : 0

  domain = "vpc"

  depends_on = [aws_internet_gateway.main]

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-nat-eip-${count.index + 1}"
      Type = "nat-eip"
    }
  )
}

# NAT Gateways
resource "aws_nat_gateway" "main" {
  count = var.vpc_init ? length(var.private_subnet_cidrs) : 0

  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = aws_subnet.public[count.index].id

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-nat-gateway-${count.index + 1}"
      Type = "nat-gateway"
    }
  )

  depends_on = [aws_internet_gateway.main]
}

#-------------------------------------------------------------------------------------------#
# Route Tables                                                                              #
#-------------------------------------------------------------------------------------------#

# Public Route Table
resource "aws_route_table" "public" {
  count = var.vpc_init ? 1 : 0

  vpc_id = aws_vpc.main[0].id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main[0].id
  }

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-public-rt"
      Type = "public-route-table"
    }
  )
}

# Public Route Table Associations
resource "aws_route_table_association" "public" {
  count = var.vpc_init ? length(aws_subnet.public) : 0

  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public[0].id
}

# Private Route Tables (one per AZ for NAT Gateway routing)
resource "aws_route_table" "private" {
  count = var.vpc_init ? length(var.private_subnet_cidrs) : 0

  vpc_id = aws_vpc.main[0].id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.main[count.index].id
  }

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-private-rt-${count.index + 1}"
      Type = "private-route-table"
      AZ   = local.azs[count.index]
    }
  )
}

# Private Route Table Associations
resource "aws_route_table_association" "private" {
  count = var.vpc_init ? length(aws_subnet.private) : 0

  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index].id
}

#-------------------------------------------------------------------------------------------#
# Security Groups                                                                           #
#-------------------------------------------------------------------------------------------#

# Default Security Group for VPC
resource "aws_security_group" "default" {
  count = var.vpc_init ? 1 : 0

  name_prefix = "${local.name_prefix}-default-"
  vpc_id      = aws_vpc.main[0].id
  description = "Default security group for ${local.name_prefix} VPC"

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow inbound traffic from within VPC
  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [var.vpc_cidr]
  }

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-default-sg"
      Type = "security-group"
    }
  )

  lifecycle {
    create_before_destroy = true
  }
}

#-------------------------------------------------------------------------------------------#
# Outputs Section                                                                           #
#-------------------------------------------------------------------------------------------#
output "vpc_data" {
  description = "The set of output data to make available to other modules."
  sensitive   = false
  value = var.vpc_init ? {
    # VPC Information
    vpc_id                = aws_vpc.main[0].id
    vpc_cidr_block        = aws_vpc.main[0].cidr_block
    vpc_arn               = aws_vpc.main[0].arn

    # Internet Gateway
    internet_gateway_id   = aws_internet_gateway.main[0].id

    # Subnets
    public_subnet_ids     = aws_subnet.public[*].id
    private_subnet_ids    = aws_subnet.private[*].id
    public_subnet_cidrs   = aws_subnet.public[*].cidr_block
    private_subnet_cidrs  = aws_subnet.private[*].cidr_block

    # Route Tables
    public_route_table_id  = aws_route_table.public[0].id
    private_route_table_ids = aws_route_table.private[*].id

    # NAT Gateways
    nat_gateway_ids       = aws_nat_gateway.main[*].id
    nat_gateway_eips      = aws_eip.nat[*].public_ip

    # Security Groups
    default_security_group_id = aws_security_group.default[0].id

    # Availability Zones
    availability_zones    = local.azs
  } : {
    # Empty values when VPC is not initialized
    vpc_id                = null
    vpc_cidr_block        = null
    vpc_arn               = null
    internet_gateway_id   = null
    public_subnet_ids     = []
    private_subnet_ids    = []
    public_subnet_cidrs   = []
    private_subnet_cidrs  = []
    public_route_table_id  = null
    private_route_table_ids = []
    nat_gateway_ids       = []
    nat_gateway_eips      = []
    default_security_group_id = null
    availability_zones    = []
  }
}