---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2024-10-04 Initial implementation for VPC creation                #
# Create Date:  2024-10-04                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Ansible main entry point for the aws_tfvpc role.                 #
# Inputs:                                                                         #
#               aws_common_state: create/present (default), destroy/absent        #
#               aws_tfvpc_config: VPC configuration parameters                    #
# Outputs:                                                                        #
#               aws_tfvpc_outputs: VPC creation results                           #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_common_state: "{{ aws_common_state }}"
      aws_tfvpc_config: "{{ aws_tfvpc_config | default({}) }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Execute aws_tfvpc_main task
  ansible.builtin.include_tasks: "{{ item }}.yml"
  loop: [aws_tfvpc_main]
