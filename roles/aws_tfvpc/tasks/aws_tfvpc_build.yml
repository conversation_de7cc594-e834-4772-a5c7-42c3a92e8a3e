---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_build.yml                                               #
# Version:                                                                        #
#               2024-10-04 Initial implementation for VPC Terraform build         #
# Create Date:  2024-10-04                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Build Terraform configuration for VPC creation                    #
# Inputs:                                                                         #
#               aws_tfvpc_common_data: VPC configuration data                     #
# Outputs:                                                                        #
#               aws_tfvpc_tempdir: Temporary directory with Terraform files       #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfvpc_common_data: "{{ aws_tfvpc_common_data }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Create temporary Terraform VPC directory
  ansible.builtin.tempfile:
    state: directory
    prefix: "galaxy."
    suffix: ".tf_vpc"
  no_log: false
  register: aws_tfvpc_tempdir
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Create Terraform VPC files from templates
  ansible.builtin.template:
    src: "/Users/<USER>/Documents/aws-main/roles/aws_tfvpc/templates/{{ item }}.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/{{ item }}"
    force: true
    mode: '0644'
  loop: [credentials, main.tf, providers.tf, terraform.tf]
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
