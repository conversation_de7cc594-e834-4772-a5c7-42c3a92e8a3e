---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_main.yml                                                #
# Version:                                                                        #
#               2024-10-04 Initial implementation for VPC creation                #
# Create Date:  2024-10-04                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Main task for VPC creation using Terraform integration            #
# Inputs:                                                                         #
#               aws_common_state: create/present (default), destroy/absent        #
#               aws_tfvpc_config: VPC configuration parameters                    #
# Outputs:                                                                        #
#               aws_tfvpc_outputs: VPC creation results                           #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_common_state: "{{ aws_common_state }}"
      aws_tfvpc_config: "{{ aws_tfvpc_config | default({}) }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Set VPC configuration defaults
  ansible.builtin.set_fact:
    aws_tfvpc_config_merged: "{{ aws_tfvpc_config_defaults | combine(aws_tfvpc_config | default({}), recursive=True) }}"
  vars:
    aws_tfvpc_config_defaults:
      vpc_cidr: "10.0.0.0/16"
      enable_dns_hostnames: true
      enable_dns_support: true
      public_subnet_cidrs:
        - "********/24"
        - "********/24"
      private_subnet_cidrs:
        - "*********/24"
        - "*********/24"
      availability_zones: []
      vpc_init: true

# ------------------------------------------------------------------------------- #
- name: Prepare common data for Terraform
  ansible.builtin.set_fact:
    aws_tfvpc_common_data: "{{ aws_common_vars | combine({
      'account': aws_common_facts.account,
      'region': aws_common_vars.aws_default_region,
      'environment': aws_common_vars.environment | default('dev'),
      'vpc_cidr': aws_tfvpc_config_merged.vpc_cidr,
      'enable_dns_hostnames': aws_tfvpc_config_merged.enable_dns_hostnames,
      'enable_dns_support': aws_tfvpc_config_merged.enable_dns_support,
      'public_subnet_cidrs': aws_tfvpc_config_merged.public_subnet_cidrs,
      'private_subnet_cidrs': aws_tfvpc_config_merged.private_subnet_cidrs,
      'availability_zones': aws_tfvpc_config_merged.availability_zones,
      'vpc_init': aws_tfvpc_config_merged.vpc_init
    }) }}"

# ------------------------------------------------------------------------------- #
- name: Execute VPC Terraform tasks
  ansible.builtin.include_tasks: "{{ item }}.yml"
  loop: [aws_tfvpc_build, aws_tfvpc_apply, aws_tfvpc_cleanup]

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfvpc_config_merged: "{{ aws_tfvpc_config_merged }}"
      aws_tfvpc_common_data: "{{ aws_tfvpc_common_data }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
