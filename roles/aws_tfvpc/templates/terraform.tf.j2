# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        terraform.tf.j2                                                   #
# Version:                                                                        #
#               2024-10-04 VPC-specific Terraform backend configuration           #
#               2024-02006 WRC. Galaxy Integration as Jinja2 template.            #
#               2021-08-10 WRC. Initial                                           #
# Create Date:  2021-08-10                                                        #
# Author:       <PERSON><PERSON><PERSON>, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#               Terraform backend configuration for VPC state management          #
#                                                                                 #
# ------------------------------------------------------------------------------- #
terraform {

  # --------------------- #
  # Backend state data    #
  # --------------------- #
  backend "s3" {
    region                    = "{{ aws_common_vars.aws_default_region }}"
    bucket                    = "{{ aws_prefix_global }}-tfstate"
    dynamodb_table            = "{{ aws_prefix }}-tfstate"
    key                       = "{{ aws_common_facts.region_id }}/{{ aws_common_vars.environment.id }}/vpc/terraform.tfstate"
    shared_credentials_files  = [ "{{ aws_tfvpc_tempdir.path }}/{{ aws_common_vars.aws_credentials_file }}" ]
    profile                   = "{{ aws_common_vars.aws_profile }}"
    encrypt                   = true
    max_retries               = {{ aws_common_vars.max_attempts }}
  }

  # --------------------- #
  # Common versions       #
  # --------------------- #
  required_version  = ">= {{ terraform_common.version }}"
  required_providers {
    {{ terraform_providers.archive.name }} = {
      source        = "{{ terraform_providers.archive.source }}/{{ terraform_providers.archive.name }}"
      version       = "{{ terraform_providers.archive.version }}"
    }
    {{ terraform_providers.aws.name }} = {
      source        = "{{ terraform_providers.aws.source }}/{{ terraform_providers.aws.name }}"
      version       = "{{ terraform_providers.aws.version }}"
    }
    {{ terraform_providers.local.name }} = {
      source        = "{{ terraform_providers.local.source }}/{{ terraform_providers.local.name }}"
      version       = "{{ terraform_providers.local.version }}"
    }
  }
}
