# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.tf.j2                                                        #
# Version:                                                                        #
#               2024-10-04 VPC-specific Terraform template                        #
# Create Date:  2024-10-04                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               VPC-specific Terraform root template for VPC creation             #
#                                                                                 #
# ------------------------------------------------------------------------------- #

#-----------------------------------------------#
# Variable definition                           #
#-----------------------------------------------#
variable "common_data" {}

#-----------------------------------------------#
# Gets the default AWS data using primary       #
# provider for this account.                    #
#-----------------------------------------------#
data "aws_partition" "current" {
  provider = aws.{{ aws_region.name }}
}

#-----------------------------------------------#
# Modules Section.  Used to instantiate         #
# modules.                                      #
#-----------------------------------------------#
module "vpc" {
  source          = "/Users/<USER>/Documents/aws-main/terraform/modules/vpc"
  providers       = { aws = aws.{{ aws_region.name }} }
  
  # Pass variables from common_data
  account                = jsondecode(var.common_data).account
  region                 = jsondecode(var.common_data).region
  environment            = jsondecode(var.common_data).environment
  vpc_cidr              = jsondecode(var.common_data).vpc_cidr
  enable_dns_hostnames  = jsondecode(var.common_data).enable_dns_hostnames
  enable_dns_support    = jsondecode(var.common_data).enable_dns_support
  public_subnet_cidrs   = jsondecode(var.common_data).public_subnet_cidrs
  private_subnet_cidrs  = jsondecode(var.common_data).private_subnet_cidrs
  availability_zones    = jsondecode(var.common_data).availability_zones
  vpc_init              = jsondecode(var.common_data).vpc_init
  
  common_data = merge ({
    aws_partition = data.aws_partition.current.partition
  }, jsondecode(var.common_data) ) # Ansible passes in common_data as JSON string so convert to TF object
}

#-------------------------------------------------------------------------------------------#
# Outputs Section.  Used to create variables usable in other modules and configurations.    #
#-------------------------------------------------------------------------------------------#
output "vpc_outputs" {
  description = "VPC module outputs"
  value = module.vpc.vpc_data
}
