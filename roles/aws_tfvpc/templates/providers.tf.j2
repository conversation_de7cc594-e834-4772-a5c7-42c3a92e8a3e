# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        providers.tf.j2                                                   #
# Version:                                                                        #
#               2024-10-04 VPC-specific provider configuration                    #
# Create Date:  2024-10-04                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Provider configuration for VPC Terraform module                   #
#                                                                                 #
# ------------------------------------------------------------------------------- #

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.33"
    }
  }
}

provider "aws" {
  alias                     = "{{ aws_common_vars.aws_default_region }}"
  region                    = "{{ aws_common_vars.aws_default_region }}"
  profile                   = "{{ aws_common_vars.aws_profile }}"
  shared_credentials_files  = [ "{{ aws_tfvpc_tempdir.path }}/{{ aws_common_vars.aws_credentials_file }}" ]
  allowed_account_ids       = [ "{{ aws_common_facts.account }}" ]
  max_retries               = "{{ aws_common_vars.max_attempts }}"
  insecure                  = false
}
