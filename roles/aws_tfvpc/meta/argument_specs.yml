---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        argument_specs.yml                                                #
# Version:                                                                        #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-24                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the argument specification for the role. With this file   #
#               present, a new task is inserted at the beginning of role          #
#               execution that will validate the parameters supplied for the role #
#               against the specification. If the parameters fail validation, the #
#               role will fail execution.                                         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
# Reference:                                                                      #
# https://docs.ansible.com/ansible/latest/collections/ansible/builtin/validate_argument_spec_module.html
argument_specs:
  aws_tfvpc_main:
    author: Casagrande, <PERSON> (US) <<EMAIL>>
    short_description: Galaxy Amazon Web Services Terraform VPC role entrypoint
    description:  Galaxy Amazon Web Services main entry point for the Terraform VPC
                  role used to create and manage AWS VPC infrastructure including
                  subnets, gateways, route tables, and security groups.

    # ----------------------------------------------- #
    # Options are “parameters” or “variables”.        #
    # ----------------------------------------------- #
    options:
      aws_common_state:
        type: str
        required: false
        default: present
        choices: [present, absent, create, destroy]
        description: The desired state of the VPC resources

      aws_tfvpc_config:
        type: dict
        required: false
        description: VPC configuration parameters
        options:
          vpc_cidr:
            type: str
            required: false
            default: "10.0.0.0/16"
            description: CIDR block for the VPC

          enable_dns_hostnames:
            type: bool
            required: false
            default: true
            description: Enable DNS hostnames in the VPC

          enable_dns_support:
            type: bool
            required: false
            default: true
            description: Enable DNS support in the VPC

          public_subnet_cidrs:
            type: list
            elements: str
            required: false
            default: ["********/24", "********/24"]
            description: CIDR blocks for public subnets

          private_subnet_cidrs:
            type: list
            elements: str
            required: false
            default: ["*********/24", "*********/24"]
            description: CIDR blocks for private subnets

          availability_zones:
            type: list
            elements: str
            required: false
            default: []
            description: Availability zones for subnets (auto-detected if empty)

          vpc_init:
            type: bool
            required: false
            default: true
            description: Whether to initialize/create the VPC resources
