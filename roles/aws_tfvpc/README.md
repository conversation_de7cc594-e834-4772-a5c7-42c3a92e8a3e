# AWS Terraform VPC (lmco.aws.aws_tfvpc)

This role is used to create and manage AWS Virtual Private Cloud (VPC) infrastructure using Terraform orchestration through Ansible. The role provides a comprehensive VPC setup including subnets, internet gateways, NAT gateways, route tables, and security groups.

## Features

- **Complete VPC Infrastructure**: Creates VPC with public and private subnets across multiple availability zones
- **Internet Connectivity**: Configures Internet Gateway for public subnets and NAT Gateways for private subnets
- **Flexible Configuration**: Supports custom CIDR blocks, subnet configurations, and availability zone selection
- **Terraform Integration**: Uses Terraform modules for infrastructure as code with Ansible orchestration
- **State Management**: Integrates with existing Terraform state management infrastructure

## Authors

This role is maintained by the [Lockheed Martin Galaxy Team](https://galaxy.pages.gitlab.global.lmco.com/documents/about/team/). Please see our [Feedback Page](https://galaxy.pages.gitlab.global.lmco.com/documents/feedback/) if you would like to pass along ideas or suggestions.

## Dependencies

Dependencies are defined at the collection level in the [Requirements](../../requirements.yml) configuration.

This role depends on:
- `lmco.aws.aws_common` - Common AWS functionality and configuration
- Terraform >= 1.0
- AWS CLI configured with appropriate credentials

## Requirements

Ansible requirements are defined in the [Role Metadata](meta/main.yml) for this role.

## Parameters

Role variables are defined in the [Argument Specification](meta/argument_specs.yml) for this role and are listed by Entry Point.

### Key Parameters

- `aws_common_state`: The desired state of VPC resources (present/absent, default: present)
- `aws_tfvpc_config`: Dictionary containing VPC configuration options:
  - `vpc_cidr`: CIDR block for the VPC (default: "10.0.0.0/16")
  - `enable_dns_hostnames`: Enable DNS hostnames (default: true)
  - `enable_dns_support`: Enable DNS support (default: true)
  - `public_subnet_cidrs`: List of CIDR blocks for public subnets
  - `private_subnet_cidrs`: List of CIDR blocks for private subnets
  - `availability_zones`: List of AZs to use (auto-detected if empty)
  - `vpc_init`: Whether to create VPC resources (default: true)

## Example Playbooks

### Basic VPC Creation

```yaml
- name: Create basic VPC
  hosts: localhost
  tasks:
    - ansible.builtin.include_role:
        name: lmco.aws.aws_tfvpc
        tasks_from: aws_tfvpc_main
      vars:
        aws_common_state: present
        aws_tfvpc_config:
          vpc_init: true
```

### Custom VPC Configuration

```yaml
- name: Create custom VPC
  hosts: localhost
  tasks:
    - ansible.builtin.include_role:
        name: lmco.aws.aws_tfvpc
        tasks_from: aws_tfvpc_main
      vars:
        aws_common_state: present
        aws_tfvpc_config:
          vpc_cidr: "**********/16"
          public_subnet_cidrs:
            - "**********/24"
            - "**********/24"
          private_subnet_cidrs:
            - "***********/24"
            - "***********/24"
          availability_zones:
            - "us-east-1a"
            - "us-east-1b"
          vpc_init: true
```

## Infrastructure Created

When `vpc_init: true`, this role creates:

1. **VPC**: Main virtual private cloud with specified CIDR block
2. **Internet Gateway**: For public internet access
3. **Public Subnets**: Subnets with public IP assignment enabled
4. **Private Subnets**: Subnets for internal resources
5. **NAT Gateways**: One per private subnet for outbound internet access
6. **Route Tables**: Public and private route tables with appropriate routes
7. **Security Groups**: Default security group for VPC
8. **Elastic IPs**: For NAT Gateways

## Testing

Test the role using the provided test playbook:

```bash
ansible-playbook roles/aws_tfvpc/tests/test.yml
```

## Examples

See the [examples directory](../../examples/) for complete playbook examples.

## License

License terms are defined at the collection level in the [LICENSE](../../LICENSE) file.
